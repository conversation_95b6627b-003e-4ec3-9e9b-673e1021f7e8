---
import GoogleAnalytics from '../components/GoogleAnalytics.astro';
import ToastNotification from '../components/ToastNotification.astro';
import OrganizationSchema from '../components/seo/OrganizationSchema.astro';
// Import essential CSS for layout and styling
import '../assets/css/variables.css';
import '../assets/css/base.css';
import '../assets/css/header.css';
// Import critical CSS for above-the-fold content
import '../assets/css/critical.css';
// Import non-critical CSS (Astro will optimize loading)
import '../assets/css/non-critical.css';

export interface Props {
	title: string;
	description?: string;
	image?: string;
	noIndex?: boolean;
}

const { title, description = "Cheers Marketplace - Quality secondhand goods in Chico, CA", image = "/cheers-marketplace-og.jpg", noIndex = false } = Astro.props;

// Generate canonical URL with trailing slash to match trailingSlash: 'always' config
const canonicalURL = new URL(Astro.url.pathname, Astro.site);
// Ensure trailing slash for consistency with Astro config
if (!canonicalURL.pathname.endsWith('/') && !canonicalURL.pathname.includes('.')) {
  canonicalURL.pathname += '/';
}
---

<!doctype html>
<html lang="en">
<head>
	<meta charset="UTF-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
	<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
	<meta name="generator" content={Astro.generator} />

	<!-- Performance optimizations for mobile -->
	<meta name="format-detection" content="telephone=no" />
	<meta name="mobile-web-app-capable" content="yes" />
	<meta name="apple-mobile-web-app-capable" content="yes" />
	<meta name="apple-mobile-web-app-status-bar-style" content="default" />

	<!-- Optimized font loading strategy - using system fonts for maximum performance -->
	<!-- System fonts provide instant loading with no external requests -->

	<!-- Preconnect to most critical domains (full connection setup) -->
	<link rel="preconnect" href="https://cdn.snipcart.com" crossorigin />
	<link rel="preconnect" href="https://www.googletagmanager.com" />
	<link rel="preconnect" href="https://images.unsplash.com" />

	<!-- Preload critical images for LCP optimization -->
	<link rel="preload" href="/logo.png" as="image" fetchpriority="high" />
	<link rel="preload" href="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=600&h=450&auto=format&fit=crop&q=80&fm=webp&dpr=1" as="image" fetchpriority="high" />

	<!-- System fonts only for maximum performance -->
	<!-- Google Fonts removed to eliminate third-party blocking -->

	<!-- Critical CSS inlined for instant rendering -->
	<style>
		/* Critical above-the-fold styles - optimized for performance */
		:root{--primary:#92400e;--primary-dark:#78350f;--primary-light:#d97706;--secondary:#1e293b;--background:#f8fafc;--light-background:#ffffff;--card-bg:#ffffff;--text:#0f172a;--text-secondary:#475569;--border:#e2e8f0;--radius:0.5rem;--radius-lg:0.75rem;--radius-xl:1rem;--container-width:1200px;--shadow:0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);--shadow-lg:0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);--font-system:system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,sans-serif;--font-serif:Georgia,"Times New Roman",serif}
		*,*::before,*::after{box-sizing:border-box}
		html,body{margin:0;padding:0;font-family:var(--font-system);background:var(--background);color:var(--text);line-height:1.6;font-size:16px;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}
		.container{max-width:var(--container-width);margin:0 auto;padding:0 2rem}
		h1,h2,h3{font-family:var(--font-serif);color:var(--text);font-weight:600;line-height:1.2;margin:0 0 1rem 0}
		h1{font-size:2.5rem}h2{font-size:2rem}h3{font-size:1.5rem}
		.site-header{background:var(--light-background);border-bottom:1px solid var(--border);position:sticky;top:0;z-index:100;backdrop-filter:blur(10px)}
		.header-flex{display:flex;align-items:center;justify-content:space-between;padding:1rem 2rem;min-height:70px}
		.logo{display:flex;align-items:center;gap:0.75rem;text-decoration:none;color:var(--text);font-weight:600;font-size:1.25rem}
		.main-nav{display:flex;gap:2rem;align-items:center}
		.main-nav a{text-decoration:none;color:var(--text-secondary);font-weight:500;transition:color 0.2s ease}
		.main-nav a:hover{color:var(--primary)}
		.btn{display:inline-flex;align-items:center;justify-content:center;padding:0.75rem 1.5rem;border:none;border-radius:var(--radius);font-weight:500;text-decoration:none;transition:all 0.2s ease;cursor:pointer;font-size:0.875rem}
		.btn.primary{background:var(--primary);color:white}
		.btn.primary:hover{background:var(--primary-dark)}
		.cart-trigger{position:relative;background:none;border:none;padding:0.5rem;cursor:pointer;color:var(--text-secondary);transition:color 0.2s ease}
		.cart-trigger:hover{color:var(--primary)}
		.cart-count{position:absolute;top:-5px;right:-5px;background:var(--primary);color:white;border-radius:50%;width:20px;height:20px;font-size:0.75rem;display:flex;align-items:center;justify-content:center;font-weight:600}
		.mobile-menu-toggle{display:none;flex-direction:column;gap:4px;background:none;border:none;padding:0.5rem;cursor:pointer}
		.hamburger-line{width:24px;height:2px;background:var(--text);transition:all 0.3s ease}
		@media (max-width: 768px){.main-nav{display:none}.mobile-menu-toggle{display:flex}.container{padding:0 1rem}.header-flex{padding:1rem}h1{font-size:2rem}h2{font-size:1.75rem}h3{font-size:1.25rem}}
		.loading{opacity:0.6;pointer-events:none}
		.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;border:0}
		*:focus-visible{outline:2px solid var(--primary);outline-offset:2px}
	</style>

	<!-- CSS optimization complete - page-specific CSS is already loaded by Astro -->



	<!-- Dynamic meta tags -->
	<title>{title}</title>
	<meta name="description" content={description} />
	<meta name="keywords" content="cheap used goods, affordable secondhand, quality thrift, budget items, Chico CA, family business, pre-owned, discount goods, bargain finds, low cost, inexpensive, economical, value shopping" />
	<meta name="author" content="Cheers Marketplace" />
	<meta name="robots" content={noIndex ? "noindex, nofollow" : "index, follow"} />
	<link rel="canonical" href={canonicalURL} />

	<!-- Open Graph / Facebook -->
	<meta property="og:type" content="website" />
	<meta property="og:url" content={canonicalURL} />
	<meta property="og:title" content={title} />
	<meta property="og:description" content={description} />
	<meta property="og:image" content={new URL(image, Astro.site)} />

	<!-- Twitter -->
	<meta name="twitter:card" content="summary_large_image" />
	<meta name="twitter:url" content={canonicalURL} />
	<meta name="twitter:title" content={title} />
	<meta name="twitter:description" content={description} />
	<meta name="twitter:image" content={new URL(image, Astro.site)} />

	<!-- Google Analytics -->
	<GoogleAnalytics measurementId="G-5FH6Y2MPJN" />

	<!-- Organization Structured Data -->
	<OrganizationSchema />



	<!-- Page-specific head content -->
	<slot name="head" />
</head>
	<body>
		<slot />

		<!-- Toast Notifications -->
		<ToastNotification />

		<!-- Snipcart v3 Integration -->
		<script define:vars={{ snipcartPublicKey: (import.meta as any).env?.PUBLIC_SNIPCART_API_KEY || 'YTY0ZTIxNjYtNDVlMC00NmUyLWFkNjItYTg3ZmNhMTc4MDQyNjM4ODczNzI4NTM0MTY0NDA4' }} is:inline>
			window.SnipcartSettings = {
				publicApiKey: snipcartPublicKey,
				loadStrategy: "on-user-interaction",
				currency: "usd",
				addProductBehavior: "none",
				modalStyle: "side"
			};

			(function(){var c,d;(d=(c=window.SnipcartSettings).version)!=null||(c.version="3.0");var s,S;(S=(s=window.SnipcartSettings).timeoutDuration)!=null||(s.timeoutDuration=2750);var l,p;(p=(l=window.SnipcartSettings).domain)!=null||(l.domain="cdn.snipcart.com");var w,u;(u=(w=window.SnipcartSettings).protocol)!=null||(w.protocol="https");var m,g;(g=(m=window.SnipcartSettings).loadCSS)!=null||(m.loadCSS=!0);var y=window.SnipcartSettings.version.includes("v3.0.0-ci")||window.SnipcartSettings.version!="3.0"&&window.SnipcartSettings.version.localeCompare("3.4.0",void 0,{numeric:!0,sensitivity:"base"})===-1,f=["focus","mouseover","touchmove","scroll","keydown"];window.LoadSnipcart=o;document.readyState==="loading"?document.addEventListener("DOMContentLoaded",r):r();function r(){window.SnipcartSettings.loadStrategy?window.SnipcartSettings.loadStrategy==="on-user-interaction"&&(f.forEach(function(t){return document.addEventListener(t,o)}),setTimeout(o,window.SnipcartSettings.timeoutDuration)):o()}var a=!1;function o(){if(a)return;a=!0;let t=document.getElementsByTagName("head")[0],n=document.querySelector("#snipcart"),i=document.querySelector('src[src^="'.concat(window.SnipcartSettings.protocol,"://").concat(window.SnipcartSettings.domain,'"][src$="snipcart.js"]')),e=document.querySelector('link[href^="'.concat(window.SnipcartSettings.protocol,"://").concat(window.SnipcartSettings.domain,'"][href$="snipcart.css"]'));n||(n=document.createElement("div"),n.id="snipcart",n.setAttribute("hidden","true"),document.body.appendChild(n)),h(n),i||(i=document.createElement("script"),i.src="".concat(window.SnipcartSettings.protocol,"://").concat(window.SnipcartSettings.domain,"/themes/v").concat(window.SnipcartSettings.version,"/default/snipcart.js"),i.async=!0,t.appendChild(i)),!e&&window.SnipcartSettings.loadCSS&&(e=document.createElement("link"),e.rel="stylesheet",e.type="text/css",e.href="".concat(window.SnipcartSettings.protocol,"://").concat(window.SnipcartSettings.domain,"/themes/v").concat(window.SnipcartSettings.version,"/default/snipcart.css"),t.prepend(e)),f.forEach(function(v){return document.removeEventListener(v,o)})}function h(t){!y||(t.dataset.apiKey=window.SnipcartSettings.publicApiKey,window.SnipcartSettings.addProductBehavior&&(t.dataset.configAddProductBehavior=window.SnipcartSettings.addProductBehavior),window.SnipcartSettings.modalStyle&&(t.dataset.configModalStyle=window.SnipcartSettings.modalStyle),window.SnipcartSettings.currency&&(t.dataset.currency=window.SnipcartSettings.currency),window.SnipcartSettings.templatesUrl&&(t.dataset.templatesUrl=window.SnipcartSettings.templatesUrl))}})();
		</script>

		<!-- Snipcart Custom Styling -->
		<style is:global>
			/* Ensure Snipcart modal appears above everything including toasts */
			.snipcart-modal__container {
				z-index: 10001 !important;
			}

			.snipcart__modal {
				z-index: 10001 !important;
			}

			/* Match Cheers Marketplace branding */
			.snipcart-layout {
				font-family: system-ui, -apple-system, sans-serif !important;
			}

			.snipcart__box--badge-highlight {
				background-color: #92400e !important;
			}

			.snipcart__button--primary {
				background-color: #92400e !important;
				border-color: #92400e !important;
			}

			.snipcart__button--primary:hover {
				background-color: #78350f !important;
				border-color: #78350f !important;
			}

			/* Side modal specific styling */
			.snipcart__modal--side {
				font-family: system-ui, -apple-system, sans-serif !important;
			}
		</style>
	</body>
</html>


