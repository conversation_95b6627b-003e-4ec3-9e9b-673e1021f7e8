---
// Mobile-optimized header with hamburger menu
import CartButton from './CartButton.astro';
---
<header class="site-header">
  <div class="container header-flex">
    <a href="/" class="logo">
    <img src="/logo.png" alt="Logo" class="logo-image" width="140" height="140" loading="eager" decoding="sync" />
    </a>

    <!-- Mobile hamburger button -->
    <button class="mobile-menu-toggle" aria-label="Toggle navigation menu" aria-expanded="false">
      <span class="hamburger-line"></span>
      <span class="hamburger-line"></span>
      <span class="hamburger-line"></span>
    </button>

    <!-- Navigation menu -->
    <nav class="main-nav" id="main-nav">
      <a href="/">Home</a>
      <a href="/products/">Products</a>
      <a href="/condition-guide/">Condition Guide</a>
      <a href="/about/">About</a>
      <a href="/faq/">FAQ</a>
    </nav>

    <!-- User Actions: Account & Cart -->
    <div class="header-actions">
      <button class="snipcart-customer-signin account-btn" aria-label="My Account">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
          <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"/>
        </svg>
        <span class="account-text">My Account</span>
      </button>
      <CartButton />
    </div>
  </div>
</header>

<!-- Mobile menu functionality -->
<script is:inline>
  document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const mainNav = document.querySelector('.main-nav');

    if (mobileMenuToggle && mainNav) {
      mobileMenuToggle.addEventListener('click', function() {
        const isOpen = mainNav.classList.contains('nav-open');

        if (isOpen) {
          mainNav.classList.remove('nav-open');
          mobileMenuToggle.classList.remove('menu-open');
          mobileMenuToggle.setAttribute('aria-expanded', 'false');
        } else {
          mainNav.classList.add('nav-open');
          mobileMenuToggle.classList.add('menu-open');
          mobileMenuToggle.setAttribute('aria-expanded', 'true');
        }
      });

      // Close menu when clicking outside
      document.addEventListener('click', function(event) {
        if (!mobileMenuToggle.contains(event.target) && !mainNav.contains(event.target)) {
          mainNav.classList.remove('nav-open');
          mobileMenuToggle.classList.remove('menu-open');
          mobileMenuToggle.setAttribute('aria-expanded', 'false');
        }
      });

      // Close menu when clicking on a nav link
      const navLinks = mainNav.querySelectorAll('a');
      navLinks.forEach(link => {
        link.addEventListener('click', function() {
          mainNav.classList.remove('nav-open');
          mobileMenuToggle.classList.remove('menu-open');
          mobileMenuToggle.setAttribute('aria-expanded', 'false');
        });
      });
    }
  });
</script>

<style>
  /* Logo styling */
  .logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    transition: opacity 0.2s ease;
    min-height: 140px; /* Prevent layout shift */
  }

  .logo-image {
    height: 140px;
    width: auto;
    max-width: 350px;
    transition: opacity 0.2s ease;
    display: block; /* Ensure no inline spacing */
  }

  .logo:hover .logo-image {
    opacity: 0.8;
  }

  /* Responsive logo sizing */
  @media (max-width: 768px) {
    .logo-image {
      height: 120px;
      max-width: 300px;
    }
  }

  @media (max-width: 480px) {
    .logo-image {
      height: 100px;
      max-width: 250px;
    }
  }

  /* Header actions positioning */
  .header-actions {
    margin-left: auto;
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  /* Account button styling */
  .account-btn {
    background: none;
    border: 1px solid var(--border, #e5e7eb);
    border-radius: 0.5rem;
    padding: 0.5rem 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text, #374151);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
  }

  .account-btn:hover {
    background: var(--light-background, #f9fafb);
    border-color: var(--primary, #3b82f6);
    color: var(--primary, #3b82f6);
  }

  .account-btn svg {
    flex-shrink: 0;
  }

  /* Ensure header flex layout accommodates actions */
  .header-flex {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
  }

  /* Mobile hamburger menu */
  .mobile-menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 44px;
    height: 44px;
    background: transparent;
    border: 1px solid #ddd;
    cursor: pointer;
    padding: 10px;
    margin-left: auto;
    z-index: 1001;
    border-radius: 6px;
  }

  .mobile-menu-toggle:hover {
    background: #f8f9fa;
    border-color: #999;
  }

  .mobile-menu-toggle:focus {
    outline: 2px solid #92400e;
    outline-offset: 2px;
  }

  .hamburger-line {
    width: 22px;
    height: 3px;
    background-color: #333333;
    margin: 3px 0;
    transition: all 0.3s ease;
    border-radius: 2px;
    display: block;
  }

  .mobile-menu-toggle.menu-open .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
  }

  .mobile-menu-toggle.menu-open .hamburger-line:nth-child(2) {
    opacity: 0;
  }

  .mobile-menu-toggle.menu-open .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
  }

  /* Mobile adjustments */
  @media (max-width: 768px) {
    .header-actions {
      order: 2;
      margin-left: 0;
      gap: 0.5rem;
    }

    .account-btn .account-text {
      display: none;
    }

    .account-btn {
      padding: 0.5rem;
    }


/* Ensure hamburger menu displays on mobile: override at end for specificity */
@media (max-width: 768px) {
  .mobile-menu-toggle {
    display: flex !important;
    order: 3;
  }
}

    .site-header .main-nav {
      display: none !important;
      position: absolute !important;
      top: 100% !important;
      left: 0 !important;
      right: 0 !important;
      background: #ffffff !important;
      border-top: 1px solid #e2e8f0 !important;
      box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1) !important;
      flex-direction: column !important;
      gap: 0 !important;
      margin: 0 !important;
      padding: 1rem 0 !important;
      z-index: 1000 !important;
      order: 4;
    }

    .site-header .main-nav.nav-open {
      display: flex !important;
      flex-direction: column !important;
    }

    .site-header .main-nav a {
      padding: 1rem 1.5rem !important;
      font-size: 1rem !important;
      border-radius: 0 !important;
      border-bottom: 1px solid var(--border-light, #f1f5f9) !important;
    }

    .site-header .main-nav a:last-child {
      border-bottom: none !important;
    }
  }

  @media (max-width: 480px) {
    .mobile-menu-toggle {
      width: 36px;
      height: 36px;
    }

    .hamburger-line {
      width: 20px;
    }
  }
</style>
